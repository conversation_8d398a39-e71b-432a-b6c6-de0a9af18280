'use client';

import Link from 'next/link';

import {
  ChevronDownIcon,
  ChevronLeftIcon,
  LightBulbIcon,
  DocumentTextIcon,
  CogIcon,
  ClipboardDocumentListIcon,
  ChatBubbleLeftEllipsisIcon,
  ArrowRightIcon,
  InformationCircleIcon,
  ArrowUturnLeftIcon,
  AdjustmentsHorizontalIcon,
  UserIcon
} from '@heroicons/react/24/outline';

import {
  snContextTemplate,
  crContextTemplate,
  workflowTemplate,
  evaluationTemplate,
  promptTemplate,
  snContextTemplate2,
  crContextTemplate2,
  evaluationTemplate2Json,
  promptTemplate2
} from './contentTemplates';

import { useState } from 'react';
import { CodeBlock, dracula } from 'react-code-blocks';
import { motion, AnimatePresence } from 'framer-motion';

export default function LiteratureAnalysisProcessPage() {
  // State for visibility of different sections
  const [isFirstIterationVisible, setIsFirstIterationVisible] = useState(false); // Default to FALSE (collapsed)
  const [isSecondIterationVisible, setIsSecondIterationVisible] = useState(false);
  const [isThirdIterationVisible, setIsThirdIterationVisible] = useState(false);
  const [showSNContext, setShowSNContext] = useState(false);
  const [showCRContext, setShowCRContext] = useState(false);
  const [showWorkflow, setShowWorkflow] = useState(false);
  const [showTemplate, setShowTemplate] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);

  // State for V2 content visibility
  const [showSNContext2, setShowSNContext2] = useState(false);
  const [showCRContext2, setShowCRContext2] = useState(false);
  const [showEvalTemplate2, setShowEvalTemplate2] = useState(false);
  const [showPrompt2, setShowPrompt2] = useState(false);

  // State for V3 content visibility
  const [showSNContext3, setShowSNContext3] = useState(false);
  const [showCRContext3, setShowCRContext3] = useState(false);
  const [showEvalTemplate3, setShowEvalTemplate3] = useState(false);
  const [showPrompt3, setShowPrompt3] = useState(false);

  return (
    <div className="min-h-screen bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 font-merriweather">
      {/* Header Section - Styled to match Home Page Banner, with Critical Review page sizing */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        {/* Enhanced Overlay from Home Page */}
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>

        {/* Content - Retain Literature Analysis Specific Text, apply CR sizing */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-white dark:text-white mb-4">
              Automated Evaluation Process
            </h1>
            {/* Optional: Add dotted line separator if desired, like on home page */}
            {/* <div className="w-full md:w-2/3 mx-auto border-t-2 border-dotted border-purple-400 dark:border-blue-400 my-8"></div> */}
            <p className="text-xl text-white dark:text-white max-w-3xl mx-auto">
              A complete description of the first and current version of my automated evaluation process.
            </p>
          </div>
        </div>
        {/* Bottom Gradient Bar from Home Page */}
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper for page sections below banner */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        {/* Navigation back to Critical Review Overview */}
        <div className="mb-8">
          <Link
            href="/critical-review"
            className="text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 flex items-center transition-colors duration-150"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Back to Critical Review Overview
          </Link>
        </div>

        {/* New Introductory Card */}
        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0 mb-8">
          <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
          <div className="p-6">
            <div className="flex items-center mb-6">
              <InformationCircleIcon className="h-7 w-7 mr-3 shrink-0 text-blue-500" />
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white flex-grow">
                Information
              </h2>
            </div>
            <div className="pl-10">
              <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
            <p className="mb-4">
              This page describes the three iterations of the process used to automate the evaluation of research papers.
            </p>
            <p className="mb-4">

            </p>
            <ul className="list-none space-y-2 mb-4 pl-2">
              <li>
                <strong className='text-gray-800 dark:text-gray-100'>First Iteration <span className="text-xs text-red-600 dark:text-red-500 font-medium tracking-wide">[legacy]</span>:</strong> This describes the initial approach, which was created before this website came into the picture. It was based upon the previous research question: "Effectively applying artificial intelligence as a music technologist.".
              </li>
              <li>
                <strong className='text-gray-800 dark:text-gray-100'>Second Iteration <span className="text-xs text-red-600 dark:text-red-500 font-medium tracking-wide">[legacy]</span>:</strong> This describes the improved approach that produced most of the content available on this website. It introduced JSON formatting and more specific evaluation criteria.
              </li>
              <li>
                <strong className='text-gray-800 dark:text-gray-100'>Third Iteration <span className="text-xs text-green-600 dark:text-green-500 font-medium tracking-wide">[active]</span>:</strong> This describes the current, most advanced version of the evaluation process with enhanced structure and comprehensive analysis capabilities.
              </li>
            </ul>
            <p>
              Explore each iteration to understand the evolution and specific components of each version of the evaluation process.
            </p>
              </section>
            </div>
          </div>
        </div>

        {/* First Iteration Expandable Section */}
        <section className="mb-8">
          {/* MAIN CARD CONTAINER FOR FIRST ITERATION - Ensures top border style */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0">
            <div className="h-1 bg-gradient-to-r from-blue-300 to-purple-300"></div>
            <button
              onClick={() => setIsFirstIterationVisible(!isFirstIterationVisible)}
              className={`w-full flex justify-between items-center text-left p-6 bg-gray-50 dark:bg-zinc-900 hover:bg-gray-100 dark:hover:bg-zinc-700 font-semibold text-xl text-gray-800 dark:text-gray-200 focus:outline-none transition-colors duration-150 ${isFirstIterationVisible ? 'rounded-t-lg' : 'rounded-lg'}`}
              aria-expanded={isFirstIterationVisible}
              aria-controls="first-iteration-content"
            >
              <span>First Iteration <span className="text-s text-red-600 dark:text-red-500 font-medium ml-2 tracking-wide">[legacy]</span></span>
              <ChevronDownIcon
                className={`w-7 h-7 transform transition-transform duration-300 ${isFirstIterationVisible ? 'rotate-180' : 'rotate-0'}`}
              />
            </button>
            {/* COLLAPSIBLE CONTENT WRAPPER */}
            <AnimatePresence>
              {isFirstIterationVisible && (
                <motion.div
                  id="first-iteration-content"
                  className="bg-white dark:bg-zinc-900 rounded-b-lg border-t border-gray-200 dark:border-gray-700 overflow-hidden"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                >
                  <div className="p-6">
                    {/* Styled Introductory Block for First Iteration */}
                    <div className="mb-8">
                        {/* Icon + Title Row - Padded to align icon with line below */}
                        <div className="flex items-center mb-3">
                          <LightBulbIcon className="h-6 w-6 text-purple-600 dark:text-purple-500 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                            Starting Out
                          </h4>
                        </div>

                        {/* Line + Paragraphs Row */}
                        <div className="flex">
                            {/* Vertical Line */}
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-purple-200 dark:bg-purple-500/30"></div>
                            </div>
                            {/* Paragraphs */}
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  This first iteration was based around my discovery of the Windsurf code editor, this editor introduced &quot;Cascade&quot; which is a agentic model that could perform multiple tasks in sequence from a single prompt. Think of analyzing multiple files, to making multiple edits, creating multiple files or a combination of these actions without human intervention.
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  Cascade would work in tandem with frontier models from other providers such as OpenAI (GPT-4o, o1, o1-mini), Google (Gemini 1.5 Pro, Gemini 2.0 Flash)or Anthropic (Claude 3 Opus, Claude 3.5 Sonnet) to facillitate their &quot;tool use&quot; (the term used at the time for models executing actions such as file creation, file editing, analyzing file contents, deleting files and even searching the web. Currently tool use has become much broader, and also natively integrated into frontier models (this means cascade is not required anymore) and includes but is not limited to: creating virtual machines and cloning repositories, executing code and generation of audio and video material.)
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  While starting work on my critical review and manually reading some papers I eventually got fatigued and could not pay as much attention to the papers, potentially causing me to miss important information. But ideally I did not want to miss anything. I wanted to read and analyze all recent papers and extract different potentially beneficial techniques and/or approaches.
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  Another factor was that the pace of research papers coming out was far too high for a single person to keep up with. For every paper read multiple would have been published. It became clear that the only way to do this was with some form of automation, and since this involved understanding natural language. LLMs were the prime candidates. This combined with the newfound  functionalities of Cascade sparked the idea to build this system and I quickly began to work on this first iteration.
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  Initially I wanted to do a &quot;double diamonds&quot; strategy with this system. First collecting relevant papers manually and letting the LLM analyze, evaluate and grade them based on user and pre-defined criteria. Then only the highest scoring papers of this first round would go on to the second round and the process would be repeated with stricter grading criteria to eventually obtain a handful of papers that the LLM deemed most useful and relevant. Then these papers would be used in my critical review.
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-8">
                                  I started out by collecting the documents that I had already written and handed in for the mid-term, the Personal Development Plan and the Supportive Narrative Outline documents. Using these documents as reference I created two markdown documents that would explain the core principles of the task the LLM would be executing:
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* === Content Sections within First Iteration === */}
                    <section id="contextual-information" className="mb-12">
                      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">The Context Documents</h2>
                      <div className="grid md:grid-cols-2 gap-8 md:items-start">
                        {/* SN Context Card */}
                        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                              <DocumentTextIcon className="h-7 w-7 mr-2 text-sky-600 dark:text-sky-400" />
                              SN_Context.md
                              <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                            </h3>
                            <button
                              onClick={() => setShowSNContext(!showSNContext)}
                              className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                            >
                              {showSNContext ? 'Hide' : 'Show'} Content
                            </button>
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 mb-4">
                            This document explains the core ideas of what we are trying to achieve and why we are doing it.
                          </p>
                          {showSNContext && (
                            <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                              <pre className="whitespace-pre-wrap font-sans">{snContextTemplate}</pre>
                            </div>
                          )}
                        </div>

                        {/* CR Context Card */}
                        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                              <DocumentTextIcon className="h-7 w-7 mr-2 text-teal-600 dark:text-teal-400" />
                              CR_Context.md
                              <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                            </h3>
                            <button
                              onClick={() => setShowCRContext(!showCRContext)}
                              className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                            >
                              {showCRContext ? 'Hide' : 'Show'} Content
                            </button>
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 mb-4">
                            This document describes the scoring system, criteria and subcriteria and how to grade them.
                          </p>
                          {showCRContext && (
                            <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                              <pre className="whitespace-pre-wrap font-sans">{crContextTemplate}</pre>
                            </div>
                          )}
                        </div>
                      </div>
                    </section>

                    <div className="mb-8">
                        <div className="flex items-center mb-3">
                          <InformationCircleIcon className="h-6 w-6 text-blue-500 dark:text-blue-400 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Defining the Sequence of Actions</h4>
                        </div>
                        <div className="flex">
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-blue-400/30 dark:bg-blue-500/30"></div>
                            </div>
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  With these documents in place, I decided that it would be a good idea to have a dedicated document to describe the flow of actions required to succesfully evaluate a paper. It also included guidelines and tips for the LLM in hopes that it would increase consistency and accuracy.
                                </p>
                            </div>
                        </div>
                    </div>

                    <section id="process-workflow" className="mb-12">
                      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">The Workflow Document</h2>
                      <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                            <CogIcon className="h-7 w-7 mr-2 text-amber-600 dark:text-amber-400" />
                            CR_Workflow.md
                            <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                          </h3>
                          <button
                            onClick={() => setShowWorkflow(!showWorkflow)}
                            className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                          >
                            {showWorkflow ? 'Hide' : 'Show'} Workflow
                          </button>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                          Step by step instructions for the LLM to evaluate a paper.
                        </p>
                        {showWorkflow && (
                          <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                            <pre className="whitespace-pre-wrap font-sans">{workflowTemplate}</pre>
                          </div>
                        )}
                      </div>
                    </section>

                    <div className="mb-8">
                        <div className="flex items-center mb-3">
                          <InformationCircleIcon className="h-6 w-6 text-orange-500 dark:text-orange-400 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Need for a Standardized Template</h4>
                        </div>
                        <div className="flex">
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-orange-400/30 dark:bg-orange-500/30"></div>
                            </div>
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  Now we needed to create the actual template that we would give to the LLM to fill out after analyzing a paper, this would ensure we got consistent outputs from each paper that could be compared to each other.
                                </p>
                            </div>
                        </div>
                    </div>

                    <section id="evaluation-template-details" className="mb-12">
                      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">The Evaluation Template</h2>
                      <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                            <ClipboardDocumentListIcon className="h-7 w-7 mr-2 text-indigo-600 dark:text-indigo-400" />
                            Evaluation_Template.md
                            <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                          </h3>
                          <button
                            onClick={() => setShowTemplate(!showTemplate)}
                            className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                          >
                            {showTemplate ? 'Hide' : 'Show'} Template
                          </button>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                          This template contains a json array for the scores, a summary section, code examples and if the paper passed a threshold score and could move on to the next round.
                        </p>
                        {showTemplate && (
                          <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                            <pre className="whitespace-pre-wrap font-sans">{evaluationTemplate}</pre>
                          </div>
                        )}
                      </div>
                    </section>

                    <div className="mb-8">
                        <div className="flex items-center mb-3">
                          <InformationCircleIcon className="h-6 w-6 text-blue-600 dark:text-blue-500 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Initial Template Design</h4>
                        </div>
                        <div className="flex">
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-blue-200 dark:bg-blue-500/30"></div>
                            </div>
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  The initial template was simple and created to be used in combination with the workflow document from above. These documents would function as the main context for the LLM. The template was intentionally kept simple because I did not want to overwhelm the LLM, and I wanted to see what kind of output it would produce from minimal instructions.
                                </p>
                            </div>
                        </div>
                    </div>

                    <section id="prompt-engineering-details" className="mb-12">
                      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">The Prompt</h2>
                      <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                            <ChatBubbleLeftEllipsisIcon className="h-7 w-7 mr-2 text-pink-600 dark:text-pink-400" />
                            Prompt.md
                            <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                          </h3>
                          <button
                            onClick={() => setShowPrompt(!showPrompt)}
                            className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                          >
                            {showPrompt ? 'Hide' : 'Show'} Prompt
                          </button>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                          This prompt allowed the LLM and Cascade to analyze each of our files, analyze the paper, create a blank evaluation file with the correct name and populate all its sections. Then also create and update a results file and update the reflections file with any notable events.
                        </p>
                        {showPrompt && (
                          <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                            <pre className="whitespace-pre-wrap font-sans">{promptTemplate}</pre>
                          </div>
                        )}
                      </div>
                    </section>
                    <div className="mb-8">
                        <div className="flex items-center mb-3">
                          <InformationCircleIcon className="h-6 w-6 text-pink-600 dark:text-pink-400 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">First Iteration Learnings & Challenges</h4>
                        </div>
                        <div className="flex">
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-pink-400/30 dark:bg-pink-500/30"></div>
                            </div>
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  This initial setup, while functional, revealed several areas for improvement. The LLM sometimes struggled with consistency, and the outputs occasionally missed nuances in the papers. It became clear that more specific instructions and a more robust template were needed to achieve the desired level of accuracy and detail. The open-ended nature of the markdown template also led to formatting inconsistencies, which would be problematic for programmatic analysis or display on a website.
                                </p>
                            </div>
                        </div>
                    </div>

                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </section>

        {/* Second Iteration Expandable Section - Apply corrected styling */}
        <section className="mb-8">
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0">
            <div className="h-1 bg-gradient-to-r from-blue-300 to-purple-300"></div>
            <button
              onClick={() => setIsSecondIterationVisible(!isSecondIterationVisible)}
              className={`w-full flex justify-between items-center text-left p-6 bg-gray-50 dark:bg-zinc-900 hover:bg-gray-100 dark:hover:bg-zinc-700 font-semibold text-xl text-gray-800 dark:text-gray-200 focus:outline-none transition-colors duration-150 ${isSecondIterationVisible ? 'rounded-t-lg' : 'rounded-lg'}`}
              aria-expanded={isSecondIterationVisible}
              aria-controls="second-iteration-content"
            >
              <span>Second Iteration <span className="text-s text-red-600 dark:text-red-500 font-medium ml-2 tracking-wide">[legacy]</span></span>
              <ChevronDownIcon
                className={`w-7 h-7 transform transition-transform duration-300 ${isSecondIterationVisible ? 'rotate-180' : 'rotate-0'}`}
              />
            </button>
            <AnimatePresence>
              {isSecondIterationVisible && (
                <motion.div
                  id="second-iteration-content"
                  className="bg-white dark:bg-zinc-900 rounded-b-lg border-t border-gray-200 dark:border-gray-700 overflow-hidden"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                >
                  <div className="p-6">
                    {/* Styled Introductory Block for Second Iteration */}
                    <div className="mb-8">
                        {/* Icon + Title Row - Padded to align icon with line below */}
                        <div className="flex items-center mb-3">
                          <AdjustmentsHorizontalIcon className="h-6 w-6 text-teal-500 dark:text-teal-400 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                            Refined Iteration Details
                          </h4>
                        </div>

                        {/* Line + Paragraphs Row */}
                        <div className="flex">
                            {/* Vertical Line */}
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-teal-400/30 dark:bg-teal-500/30"></div>
                            </div>
                            {/* Paragraphs */}
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  Through the process of developing the website I realized the weaknesses of my first approach, the markdown template gave the LLM too much freedom, its outputs had many slight inconsistencies in formatting which prevented cleanly displaying all the content on the website unless I manually edited these evaluation files. I Also noticed that the LLM would sometimes only analyze a chunk of the paper while filling in the evaluation template as if it had read everything. To fix these issues, it seemed appropriate to create a improved version of the system.
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  I began to work on the second iteration, attempting to create a simplified and more consistent evaluation process and to fix the issues presented by the original approach.
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  Some time had passed between the first and second iteration and new frontier models had been released, these models performed better across the board and most importantly had larger input sizes and context windows.
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  I also found the original scoring criteria too generic and decided to change these to be more specific towards my personal day to day tasks as a audio software developer.
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  In order to ensure consistency accross all evaluation files I changed the template to be entirely in JSON format, this more rigid structure in combination with more capable models and better instructions ensured that all evaluation files were now created using the same formatting. To ensure that the LLM would analyze the entire paper and not parts of it I utilized the larger input sizes to include all our context files and the paper contents directly into a single prompt, this approach ensured that the LLM would not accidentally analyze only a part of the paper.
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-8">
                                  With increased input sizes came increased output sizes, I wanted to extract more information for each paper and created more sections in the evaluation template. I introduced a &quot;methodologicalDeepDive&quot; section and a &quot;resultsInsights&quot; section whose contents would be used to populate the Methods and the Results page of the website. This way we would extract Implementable methods from each paper with step by step instructions and a practical example with code snippets. In the results page we would describe the claimed outcomes of a paper and contextualize its finding towards what it would mean for audio software developers.
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Context Documents for Second Iteration */}
                    <section id="contextual-information-v2" className="mb-12">
                      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">The Context Documents</h2>
                      <div className="grid md:grid-cols-2 gap-8 md:items-start">
                        {/* SN Context Card V2 */}
                        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                              <DocumentTextIcon className="h-7 w-7 mr-2 text-blue-600 dark:text-blue-400" />
                              SN_Context.md
                              <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                            </h3>
                            <button
                              onClick={() => setShowSNContext2(!showSNContext2)}
                              className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                            >
                              {showSNContext2 ? 'Hide' : 'Show'} Content
                            </button>
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 mb-4">
                            This document remained the except for using the definitive research question.
                          </p>
                          {showSNContext2 && (
                            <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                              <pre className="whitespace-pre-wrap font-sans">{snContextTemplate2}</pre>
                            </div>
                          )}
                        </div>

                        {/* CR Context Card V2 */}
                        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                              <DocumentTextIcon className="h-7 w-7 mr-2 text-cyan-600 dark:text-cyan-400" />
                              CR_Context.md
                              <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                            </h3>
                            <button
                              onClick={() => setShowCRContext2(!showCRContext2)}
                              className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                            >
                              {showCRContext2 ? 'Hide' : 'Show'} Content
                            </button>
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 mb-4">
                            Adapted scoring criteria to better suit my typical tasks as an audio software developer.
                          </p>
                          {showCRContext2 && (
                            <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                              <pre className="whitespace-pre-wrap font-sans">{crContextTemplate2}</pre>
                            </div>
                          )}
                        </div>
                      </div>
                    </section>
                    <div className="mb-8">
                        <div className="flex items-center mb-3">
                          <InformationCircleIcon className="h-6 w-6 text-blue-500 dark:text-blue-400 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Streamlined Context and Workflow</h4>
                        </div>
                        <div className="flex">
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-blue-400/30 dark:bg-blue-500/30"></div>
                            </div>
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  When using a single prompt instead of a chain of instructions, the workflow document was no longer needed. This decreased the total amount of tokens our instructions occupied in the model's context window and allowed for slightly larger papers to be evaluated. In practice I only ran into a paper exceeding the input size once, this paper was ~22K lines and was therefore not evaluated with this system.
                                </p>
                            </div>
                        </div>
                    </div>
                    {/* Evaluation Template V2 Section */}
                    <section id="evaluation-template-v2-details" className="mb-12">
                      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">The Evaluation Template</h2>
                      <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                            <ClipboardDocumentListIcon className="h-7 w-7 mr-2 text-emerald-600 dark:text-emerald-400" />
                            Evaluation_Template.json
                            <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                          </h3>
                          <button
                            onClick={() => setShowEvalTemplate2(!showEvalTemplate2)}
                            className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                          >
                            {showEvalTemplate2 ? 'Hide' : 'Show'} Template
                          </button>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                          The standardized JSON template used for the second iteration, focusing on methodological deep dives.
                        </p>
                        {showEvalTemplate2 && (
                          <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                            <pre className="whitespace-pre-wrap font-sans">{evaluationTemplate2Json}</pre>
                          </div>
                        )}
                      </div>
                    </section>
                    <div className="mb-8">
                        <div className="flex items-center mb-3">
                          <InformationCircleIcon className="h-6 w-6 text-emerald-600 dark:text-emerald-400 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Advantages of JSON Template</h4>
                        </div>
                        <div className="flex">
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-emerald-400/30 dark:bg-emerald-500/30"></div>
                            </div>
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  JSON turned out to be an excellent candidate for keeping the models output in check, perhaps other formats could work too such as XML. Using JSON worked correctly and did not require me to do any further exploration.
                                </p>
                            </div>
                        </div>
                    </div>
                    {/* Prompt Engineering V2 Section */}
                    <section id="prompt-engineering-v2-details" className="mb-12">
                      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">The Prompt</h2>
                      <div className="bg-gray-50 dark:bg-zinc-800 p-6 rounded-lg shadow-lg border border-gray-300 dark:border-gray-600 border-t-4 border-t-lime-500 dark:border-t-lime-400">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                            <ChatBubbleLeftEllipsisIcon className="h-7 w-7 mr-2 text-lime-600 dark:text-lime-400" />
                            Prompt.md
                            <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                          </h3>
                          <button
                            onClick={() => setShowPrompt2(!showPrompt2)}
                            className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                          >
                            {showPrompt2 ? 'Hide' : 'Show'} Prompt
                          </button>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                          The prompt used was all of the above files combined and would be roughly ~600 lines long excluding the actual paper.
                        </p>
                        {showPrompt2 && (
                          <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                            <pre className="whitespace-pre-wrap font-sans">{promptTemplate2}</pre>
                          </div>
                        )}
                      </div>
                    </section>
                    <div className="mb-8">
                        <div className="flex items-center mb-3">
                          <InformationCircleIcon className="h-6 w-6 text-lime-600 dark:text-lime-400 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Second Iteration Outcomes & Next Steps</h4>
                        </div>
                        <div className="flex">
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-lime-400/30 dark:bg-lime-500/30"></div>
                            </div>
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  This approach resulted in consistent and more accurate evaluations by the model, The scores were much lower then before, since the LLM was now evaluating the papers against more specific criteria. With this version It is still required to manually copy and paste the prompt and the paper contents. <br /><br />
                                  Now that I ensured consistency in formatting I could shift my focus to the website to ensure that it displayed the information correctly.
                                </p>
                            </div>
                        </div>
                    </div>

                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </section>

        {/* Third Iteration Expandable Section */}
        <section className="mb-8">
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0">
            <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
            <button
              onClick={() => setIsThirdIterationVisible(!isThirdIterationVisible)}
              className={`w-full flex justify-between items-center text-left p-6 bg-gray-50 dark:bg-zinc-900 hover:bg-gray-100 dark:hover:bg-zinc-700 font-semibold text-xl text-gray-800 dark:text-gray-200 focus:outline-none transition-colors duration-150 ${isThirdIterationVisible ? 'rounded-t-lg' : 'rounded-lg'}`}
              aria-expanded={isThirdIterationVisible}
              aria-controls="third-iteration-content"
            >
              <span>Third Iteration <span className="text-s text-green-600 dark:text-green-500 font-medium ml-2 tracking-wide">[active]</span></span>
              <ChevronDownIcon
                className={`w-7 h-7 transform transition-transform duration-300 ${isThirdIterationVisible ? 'rotate-180' : 'rotate-0'}`}
              />
            </button>
            <AnimatePresence>
              {isThirdIterationVisible && (
                <motion.div
                  id="third-iteration-content"
                  className="bg-white dark:bg-zinc-900 rounded-b-lg border-t border-gray-200 dark:border-gray-700 overflow-hidden"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                >
                  <div className="p-6">
                    {/* Styled Introductory Block for Third Iteration */}
                    <div className="mb-8">
                        {/* Icon + Title Row - Padded to align icon with line below */}
                        <div className="flex items-center mb-3">
                          <AdjustmentsHorizontalIcon className="h-6 w-6 text-green-500 dark:text-green-400 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                            Enhanced Evaluation Framework
                          </h4>
                        </div>

                        {/* Line + Paragraphs Row */}
                        <div className="flex">
                            {/* Vertical Line */}
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-green-400/30 dark:bg-green-500/30"></div>
                            </div>
                            {/* Paragraphs */}
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  [Placeholder: Description of the third iteration improvements and motivations will be added here. This section will explain the evolution from the second iteration and the specific enhancements made to the evaluation process.]
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  [Placeholder: Additional context about the third iteration development process, new features, and improvements over previous versions.]
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  [Placeholder: Technical details about the enhanced evaluation framework and its capabilities.]
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-8">
                                  [Placeholder: Information about the new template structure and evaluation criteria used in the third iteration.]
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Context Documents for Third Iteration */}
                    <section id="contextual-information-v3" className="mb-12">
                      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">The Context Documents</h2>
                      <div className="grid md:grid-cols-2 gap-8 md:items-start">
                        {/* SN Context Card V3 */}
                        <div className="bg-gray-50 dark:bg-zinc-800 p-6 rounded-lg shadow-lg border border-gray-300 dark:border-gray-600 border-t-4 border-t-green-500 dark:border-t-green-400">
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                              <DocumentTextIcon className="h-7 w-7 mr-2 text-green-600 dark:text-green-400" />
                              SN_Context.md
                              <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                            </h3>
                            <button
                              onClick={() => setShowSNContext3(!showSNContext3)}
                              className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                            >
                              {showSNContext3 ? 'Hide' : 'Show'} Content
                            </button>
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 mb-4">
                            [Placeholder: Description of the updated supportive narrative context document for the third iteration.]
                          </p>
                          {showSNContext3 && (
                            <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                              <pre className="whitespace-pre-wrap font-sans">[Placeholder: SN Context V3 content will be added here]</pre>
                            </div>
                          )}
                        </div>

                        {/* CR Context Card V3 */}
                        <div className="bg-gray-50 dark:bg-zinc-800 p-6 rounded-lg shadow-lg border border-gray-300 dark:border-gray-600 border-t-4 border-t-emerald-500 dark:border-t-emerald-400">
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                              <DocumentTextIcon className="h-7 w-7 mr-2 text-emerald-600 dark:text-emerald-400" />
                              CR_Context.md
                              <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                            </h3>
                            <button
                              onClick={() => setShowCRContext3(!showCRContext3)}
                              className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                            >
                              {showCRContext3 ? 'Hide' : 'Show'} Content
                            </button>
                          </div>
                          <p className="text-gray-600 dark:text-gray-400 mb-4">
                            [Placeholder: Description of the enhanced evaluation criteria and scoring system for the third iteration.]
                          </p>
                          {showCRContext3 && (
                            <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                              <pre className="whitespace-pre-wrap font-sans">[Placeholder: CR Context V3 content will be added here]</pre>
                            </div>
                          )}
                        </div>
                      </div>
                    </section>
                    <div className="mb-8">
                        <div className="flex items-center mb-3">
                          <InformationCircleIcon className="h-6 w-6 text-green-500 dark:text-green-400 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Enhanced Template Structure</h4>
                        </div>
                        <div className="flex">
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-green-400/30 dark:bg-green-500/30"></div>
                            </div>
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  [Placeholder: Explanation of the improvements made to the template structure in the third iteration, including new sections and enhanced organization.]
                                </p>
                            </div>
                        </div>
                    </div>
                    {/* Evaluation Template V3 Section */}
                    <section id="evaluation-template-v3-details" className="mb-12">
                      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">The Evaluation Template</h2>
                      <div className="bg-gray-50 dark:bg-zinc-800 p-6 rounded-lg shadow-lg border border-gray-300 dark:border-gray-600 border-t-4 border-t-green-600 dark:border-t-green-500">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                            <ClipboardDocumentListIcon className="h-7 w-7 mr-2 text-green-600 dark:text-green-500" />
                            Evaluation_Template_V3.json
                            <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                          </h3>
                          <button
                            onClick={() => setShowEvalTemplate3(!showEvalTemplate3)}
                            className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                          >
                            {showEvalTemplate3 ? 'Hide' : 'Show'} Template
                          </button>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                          [Placeholder: Description of the enhanced JSON template used for the third iteration, with improved structure and comprehensive evaluation sections.]
                        </p>
                        {showEvalTemplate3 && (
                          <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                            <pre className="whitespace-pre-wrap font-sans">[Placeholder: Evaluation Template V3 JSON structure will be added here]</pre>
                          </div>
                        )}
                      </div>
                    </section>
                    <div className="mb-8">
                        <div className="flex items-center mb-3">
                          <InformationCircleIcon className="h-6 w-6 text-green-600 dark:text-green-500 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Advanced Template Features</h4>
                        </div>
                        <div className="flex">
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-green-400/30 dark:bg-green-500/30"></div>
                            </div>
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  [Placeholder: Details about the advanced features and improvements in the third iteration template, including new evaluation sections and enhanced data structure.]
                                </p>
                            </div>
                        </div>
                    </div>
                    {/* Prompt Engineering V3 Section */}
                    <section id="prompt-engineering-v3-details" className="mb-12">
                      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">The Prompt</h2>
                      <div className="bg-gray-50 dark:bg-zinc-800 p-6 rounded-lg shadow-lg border border-gray-300 dark:border-gray-600 border-t-4 border-t-teal-500 dark:border-t-teal-400">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                            <ChatBubbleLeftEllipsisIcon className="h-7 w-7 mr-2 text-teal-600 dark:text-teal-400" />
                            Prompt_V3.md
                            <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                          </h3>
                          <button
                            onClick={() => setShowPrompt3(!showPrompt3)}
                            className="text-sm text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
                          >
                            {showPrompt3 ? 'Hide' : 'Show'} Prompt
                          </button>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                          [Placeholder: Description of the refined prompt used in the third iteration, with enhanced instructions and improved evaluation guidance.]
                        </p>
                        {showPrompt3 && (
                          <div className="mt-4 bg-gray-100 dark:bg-zinc-900 p-4 rounded-lg text-sm max-h-96 overflow-y-auto prose dark:prose-invert prose-sm custom-styled-scrollbar">
                            <pre className="whitespace-pre-wrap font-sans">[Placeholder: Prompt V3 content will be added here]</pre>
                          </div>
                        )}
                      </div>
                    </section>
                    <div className="mb-8">
                        <div className="flex items-center mb-3">
                          <InformationCircleIcon className="h-6 w-6 text-teal-600 dark:text-teal-400 mr-3 shrink-0" />
                          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Third Iteration Achievements & Future Development</h4>
                        </div>
                        <div className="flex">
                            <div className="w-0.5 mr-0 shrink-0 self-stretch relative">
                              <div className="absolute top-0 left-0 right-0 bottom-4 bg-teal-400/30 dark:bg-teal-500/30"></div>
                            </div>
                            <div className="pl-9">
                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                                  [Placeholder: Summary of the achievements and improvements in the third iteration, along with plans for future development and refinement of the evaluation process.]
                                </p>
                            </div>
                        </div>
                    </div>

                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </section>

      </div> {/* Close Main Content Wrapper */}
    </div>
  );
}
